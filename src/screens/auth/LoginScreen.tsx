import React, { useState } from "react";
import { View, Text, Alert, ScrollView, KeyboardAvoidingView, Platform } from "react-native";
import { LinearGradient } from "expo-linear-gradient";
import { authService } from "../../services/auth";
import { useNavigation } from "@react-navigation/native";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";
import { AuthStackParamList } from "../../types/navigation";
import Button from "../../components/ui/Button";
import Input from "../../components/ui/Input";
import Card from "../../components/ui/Card";

type LoginScreenNavigationProp = NativeStackNavigationProp<AuthStackParamList, "Login">;

const LoginScreen: React.FC = () => {
    const [email, setEmail] = useState("");
    const [password, setPassword] = useState("");
    const [loading, setLoading] = useState(false);
    const [errors, setErrors] = useState<{ email?: string; password?: string }>({});
    const navigation = useNavigation<LoginScreenNavigationProp>();

    const validateForm = () => {
        const newErrors: { email?: string; password?: string } = {};

        if (!email) {
            newErrors.email = "E-posta adresi gerekli";
        } else if (!/\S+@\S+\.\S+/.test(email)) {
            newErrors.email = "Geçerli bir e-posta adresi girin";
        }

        if (!password) {
            newErrors.password = "Şifre gerekli";
        } else if (password.length < 6) {
            newErrors.password = "Şifre en az 6 karakter olmalı";
        }

        setErrors(newErrors);
        return Object.keys(newErrors).length === 0;
    };

    const handleLogin = async () => {
        if (!validateForm()) return;

        setLoading(true);
        try {
            await authService.signIn(email, password);
        } catch (error: any) {
            Alert.alert("Giriş Hatası", error.message);
        } finally {
            setLoading(false);
        }
    };

    return (
        <LinearGradient colors={["#667eea", "#764ba2"]} className="flex-1">
            <KeyboardAvoidingView
                behavior={Platform.OS === "ios" ? "padding" : "height"}
                className="flex-1"
            >
                <ScrollView
                    contentContainerStyle={{ flexGrow: 1 }}
                    showsVerticalScrollIndicator={false}
                    className="flex-1"
                >
                    <View className="flex-1 justify-center px-6 py-8">
                        {/* Header */}
                        <View className="items-center mb-12">
                            <View className="bg-white/20 backdrop-blur-sm rounded-full p-6 mb-6">
                                <Text className="text-6xl">📚</Text>
                            </View>
                            <Text className="text-4xl font-bold text-white mb-2">LinguaRead</Text>
                            <Text className="text-white/80 text-lg text-center">
                                Dil öğrenme yolculuğunuza hoş geldiniz
                            </Text>
                        </View>

                        {/* Login Form */}
                        <Card variant="glass" className="mb-6">
                            <Text className="text-2xl font-bold text-gray-800 mb-6 text-center">
                                Giriş Yap
                            </Text>

                            <Input
                                label="E-posta"
                                placeholder="<EMAIL>"
                                value={email}
                                onChangeText={setEmail}
                                keyboardType="email-address"
                                autoCapitalize="none"
                                icon="📧"
                                error={errors.email}
                            />

                            <Input
                                label="Şifre"
                                placeholder="Şifrenizi girin"
                                value={password}
                                onChangeText={setPassword}
                                secureTextEntry
                                icon="🔒"
                                error={errors.password}
                            />

                            <Button
                                title="Giriş Yap"
                                onPress={handleLogin}
                                loading={loading}
                                fullWidth
                                size="large"
                                className="mb-4"
                            />

                            <Button
                                title="Şifremi Unuttum"
                                onPress={() => navigation.navigate("ForgotPassword")}
                                variant="outline"
                                fullWidth
                                size="medium"
                            />
                        </Card>

                        {/* Register Link */}
                        <Card variant="glass">
                            <View className="flex-row justify-center items-center">
                                <Text className="text-gray-700 mr-2">Hesabınız yok mu?</Text>
                                <Button
                                    title="Kayıt Ol"
                                    onPress={() => navigation.navigate("Register")}
                                    variant="secondary"
                                    size="small"
                                />
                            </View>
                        </Card>
                    </View>
                </ScrollView>
            </KeyboardAvoidingView>
        </LinearGradient>
    );
};

export default LoginScreen;
