import React from 'react';
import { View, Text } from 'react-native';

interface TabIconProps {
  name: string;
  emoji: string;
  focused: boolean;
  color?: string;
}

const TabIcon: React.FC<TabIconProps> = ({ 
  name, 
  emoji, 
  focused, 
  color = '#3b82f6' 
}) => {
  return (
    <View className="items-center justify-center">
      <View className={`
        w-12 h-8 items-center justify-center rounded-full
        ${focused ? 'bg-blue-100' : 'bg-transparent'}
        transition-all duration-200
      `}>
        <Text className={`
          text-xl
          ${focused ? 'transform scale-110' : 'transform scale-100'}
          transition-transform duration-200
        `}>
          {emoji}
        </Text>
      </View>
      
      <Text className={`
        text-xs font-medium mt-1
        ${focused ? 'text-blue-600' : 'text-gray-500'}
        transition-colors duration-200
      `}>
        {name}
      </Text>
      
      {focused && (
        <View className="w-1 h-1 bg-blue-600 rounded-full mt-1" />
      )}
    </View>
  );
};

export default TabIcon;
