{"name": "linguaread", "version": "1.0.0", "main": "index.ts", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@react-native-async-storage/async-storage": "^2.1.2", "@react-native-firebase/app": "^22.2.1", "@react-native-firebase/auth": "^22.2.1", "@react-native-firebase/firestore": "^22.2.1", "@react-native-firebase/storage": "^22.2.1", "@react-navigation/bottom-tabs": "^7.4.2", "@react-navigation/native": "^7.1.14", "@react-navigation/native-stack": "^7.3.21", "@react-navigation/stack": "^7.4.2", "expo": "~53.0.17", "expo-status-bar": "~2.2.3", "firebase": "^11.10.0", "nativewind": "^4.1.23", "react": "19.0.0", "react-dom": "^19.0.0", "react-native": "0.79.5", "react-native-safe-area-context": "^5.4.0", "react-native-screens": "^4.11.1", "react-native-web": "^0.20.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~19.0.10", "@types/react-dom": "^19.1.6", "tailwindcss": "^3.4.17", "typescript": "~5.8.3"}, "private": true}